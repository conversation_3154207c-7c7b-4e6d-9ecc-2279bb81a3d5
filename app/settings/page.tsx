import { currentUser } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import { ApiKey, apiKeyOperations } from '@/lib/db';
import { getCurrentDbUser } from '@/lib/auth';
import { UserProfile } from '@clerk/nextjs';

export default async function SettingsPage() {
    const user = await currentUser();

    if (!user) {
        redirect('/sign-in');
    }

    // Get user data from database
    const dbUser = await getCurrentDbUser();

    // Get user's API keys
    let apiKeys: ApiKey[] = [];
    try {
        apiKeys = await apiKeyOperations.findByUser(user.id);
    } catch (error) {
        console.error('Error fetching API keys:', error);
    }

    return (
        <div className="min-h-screen bg-gray-50">
            <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                <div className="px-4 py-6 sm:px-0">
                    <div className="mb-8">
                        <h1 className="text-3xl font-bold text-gray-900">
                            Account Settings
                        </h1>
                        <p className="mt-2 text-gray-600">
                            Manage your account, subscription, and API access
                        </p>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        {/* Profile Settings */}
                        <div className="lg:col-span-2">
                            <div className="bg-white rounded-lg shadow">
                                <div className="px-6 py-4 border-b border-gray-200">
                                    <h2 className="text-xl font-semibold">
                                        Profile Settings
                                    </h2>
                                </div>
                                <div className="p-6">
                                    <UserProfile
                                        appearance={{
                                            elements: {
                                                card: 'shadow-none border-0',
                                                navbar: 'hidden',
                                                navbarMobileMenuButton:
                                                    'hidden',
                                                headerTitle: 'hidden',
                                                headerSubtitle: 'hidden',
                                            },
                                        }}
                                    />
                                </div>
                            </div>
                        </div>

                        {/* Account Information & API Keys */}
                        <div className="space-y-6">
                            {/* Account Info */}
                            {dbUser && (
                                <div className="bg-white rounded-lg shadow">
                                    <div className="px-6 py-4 border-b border-gray-200">
                                        <h2 className="text-xl font-semibold">
                                            Account Information
                                        </h2>
                                    </div>
                                    <div className="p-6 space-y-4">
                                        <div>
                                            <p className="text-sm text-gray-600">
                                                Subscription Plan
                                            </p>
                                            <p className="font-medium text-lg">
                                                {dbUser.subscriptionPlan}
                                            </p>
                                        </div>
                                        <div>
                                            <p className="text-sm text-gray-600">
                                                Credits Available
                                            </p>
                                            <p className="font-medium text-lg text-green-600">
                                                {dbUser.creditBalance}
                                            </p>
                                        </div>
                                        <div>
                                            <p className="text-sm text-gray-600">
                                                Monthly Credits Used
                                            </p>
                                            <p className="font-medium">
                                                {dbUser.monthlyCreditsUsed} /{' '}
                                                {dbUser.monthlyCreditsLimit}
                                            </p>
                                        </div>
                                        <div>
                                            <p className="text-sm text-gray-600">
                                                Total Credits Used
                                            </p>
                                            <p className="font-medium">
                                                {dbUser.totalCreditsUsed}
                                            </p>
                                        </div>
                                        <div>
                                            <p className="text-sm text-gray-600">
                                                Member Since
                                            </p>
                                            <p className="font-medium">
                                                {new Date(
                                                    dbUser.createdAt
                                                ).toLocaleDateString()}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* API Keys */}
                            <div className="bg-white rounded-lg shadow">
                                <div className="px-6 py-4 border-b border-gray-200">
                                    <h2 className="text-xl font-semibold">
                                        API Keys
                                    </h2>
                                </div>
                                <div className="p-6">
                                    {apiKeys.length > 0 ? (
                                        <div className="space-y-3">
                                            {apiKeys.map((apiKey) => (
                                                <div
                                                    key={apiKey.id}
                                                    className="border rounded-lg p-4"
                                                >
                                                    <div className="flex justify-between items-start">
                                                        <div>
                                                            <p className="font-medium">
                                                                {apiKey.name}
                                                            </p>
                                                            <p className="text-sm text-gray-600">
                                                                Created:{' '}
                                                                {new Date(
                                                                    apiKey.createdAt
                                                                ).toLocaleDateString()}
                                                            </p>
                                                            <p className="text-sm text-gray-600">
                                                                Requests:{' '}
                                                                {
                                                                    apiKey.requestCount
                                                                }
                                                            </p>
                                                            {apiKey.lastUsedAt && (
                                                                <p className="text-sm text-gray-600">
                                                                    Last used:{' '}
                                                                    {new Date(
                                                                        apiKey.lastUsedAt
                                                                    ).toLocaleDateString()}
                                                                </p>
                                                            )}
                                                        </div>
                                                        <span
                                                            className={`px-2 py-1 text-xs rounded-full ${
                                                                apiKey.isActive
                                                                    ? 'bg-green-100 text-green-800'
                                                                    : 'bg-red-100 text-red-800'
                                                            }`}
                                                        >
                                                            {apiKey.isActive
                                                                ? 'Active'
                                                                : 'Inactive'}
                                                        </span>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <div className="text-center py-6">
                                            <p className="text-gray-600 mb-4">
                                                No API keys created yet
                                            </p>
                                            <button className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                                                Create API Key
                                            </button>
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* Quick Actions */}
                            <div className="bg-white rounded-lg shadow">
                                <div className="px-6 py-4 border-b border-gray-200">
                                    <h2 className="text-xl font-semibold">
                                        Quick Actions
                                    </h2>
                                </div>
                                <div className="p-6 space-y-3">
                                    <button className="w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                                        Upgrade Plan
                                    </button>
                                    <button className="w-full bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                                        View Credit History
                                    </button>
                                    <button className="w-full bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                                        Download Usage Report
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
